import asyncio
import json
import re
import os
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from datetime import datetime
from urllib.parse import urljoin

class TuneCasterSequentialScraper:
    def __init__(self):
        self.base_url = "https://tunecaster.com"
        self.all_chart_data = []
        self.progress_file = 'data/scraper_progress.json'
        self.url_log_file = 'data/url_log.txt'
        self.processed_urls = set()
        self.current_pop_url = None
        self.current_rock_url = None
        self.all_discovered_pop_urls = []
        self.all_discovered_rock_urls = []

        # Define start and end URLs
        self.pop_begin_url = "https://tunecaster.com/charts/50/week5952.html"
        self.pop_end_url = "https://tunecaster.com/charts/10/week1526.html"
        self.rock_begin_url = "https://tunecaster.com/charts/70/rock7948.html"
        self.rock_end_url = "https://tunecaster.com/charts/10/rock1526.html"
    
    def log_discovered_url(self, url, chart_type, source="navigation"):
        """Log all discovered URLs for tracking"""
        try:
            os.makedirs('data', exist_ok=True)
            with open(self.url_log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().isoformat()
                f.write(f"{timestamp} | {chart_type.upper()} | {source} | {url}\n")
            
            # Add to internal lists
            if chart_type == 'pop' and url not in self.all_discovered_pop_urls:
                self.all_discovered_pop_urls.append(url)
            elif chart_type == 'rock' and url not in self.all_discovered_rock_urls:
                self.all_discovered_rock_urls.append(url)
                
        except Exception as e:
            print(f"Error logging URL: {e}")
    
    def load_progress(self):
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress = json.load(f)
                    self.processed_urls = set(progress.get('processed_urls', []))
                    self.current_pop_url = progress.get('current_pop_url', self.pop_begin_url)
                    self.current_rock_url = progress.get('current_rock_url', self.rock_begin_url)
                    self.all_discovered_pop_urls = progress.get('all_discovered_pop_urls', [])
                    self.all_discovered_rock_urls = progress.get('all_discovered_rock_urls', [])
                    
                    print(f"Loaded progress: {len(self.processed_urls)} URLs already processed")
                    print(f"Current Pop URL: {self.current_pop_url}")
                    print(f"Current Rock URL: {self.current_rock_url}")
                    print(f"Discovered Pop URLs: {len(self.all_discovered_pop_urls)}")
                    print(f"Discovered Rock URLs: {len(self.all_discovered_rock_urls)}")
                    return True
            except Exception as e:
                print(f"Could not load progress: {e}")
        
        # Initialize with begin URLs if no progress
        self.current_pop_url = self.pop_begin_url
        self.current_rock_url = self.rock_begin_url
        return False
    
    def save_progress(self, current_url, chart_type):
        try:
            os.makedirs('data', exist_ok=True)
            self.processed_urls.add(current_url)
            
            if chart_type == 'pop':
                self.current_pop_url = current_url
            elif chart_type == 'rock':
                self.current_rock_url = current_url
                
            progress = {
                'processed_urls': list(self.processed_urls),
                'current_pop_url': self.current_pop_url,
                'current_rock_url': self.current_rock_url,
                'all_discovered_pop_urls': self.all_discovered_pop_urls,
                'all_discovered_rock_urls': self.all_discovered_rock_urls,
                'last_processed': current_url,
                'last_chart_type': chart_type,
                'timestamp': datetime.now().isoformat()
            }
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Could not save progress: {e}")
    
    async def find_next_week_url(self, page, current_url):
        """Find the next week URL by looking for next week link with multiple strategies"""
        try:
            # Get all links on the page
            links = await page.evaluate('''
                () => {
                    const links = Array.from(document.querySelectorAll('a[href]'));
                    return links.map(link => ({
                        href: link.href,
                        text: link.textContent.trim().toLowerCase(),
                        innerHTML: link.innerHTML.toLowerCase(),
                        title: link.title || '',
                        className: link.className || ''
                    }));
                }
            ''')
            
            # Extract current week number from URL
            current_match = re.search(r'(?:week|rock)(\d{4})\.html', current_url)
            if not current_match:
                print(f"Could not extract week number from current URL: {current_url}")
                return None
            
            current_week_num = int(current_match.group(1))
            chart_type = 'rock' if 'rock' in current_url else 'week'
            
            print(f"Current week: {current_week_num}, Chart type: {chart_type}")
            
            # Strategy 1: Look for next week (current + 1)
            next_week_num = current_week_num + 1
            next_week_str = f"{next_week_num:04d}"
            
            print(f"Looking for next week: {chart_type}{next_week_str}.html")
            
            # Find link with exact next week number
            for link in links:
                if f"{chart_type}{next_week_str}.html" in link['href']:
                    next_url = link['href']
                    if next_url.startswith('/'):
                        next_url = urljoin(self.base_url, next_url)
                    print(f"Found exact next week URL: {next_url}")
                    # Log this discovered URL
                    self.log_discovered_url(next_url, 'pop' if chart_type == 'week' else 'rock', 'exact_match')
                    return next_url
            
            # Strategy 2: Look for "next" text in links
            next_keywords = ['next week', 'next', '→', '>', 'forward', 'ahead']
            for link in links:
                link_text = link['text'].lower()
                for keyword in next_keywords:
                    if keyword in link_text:
                        # Check if it's the right chart type
                        if (chart_type == 'week' and 'week' in link['href']) or (chart_type == 'rock' and 'rock' in link['href']):
                            next_url = link['href']
                            if next_url.startswith('/'):
                                next_url = urljoin(self.base_url, next_url)
                            print(f"Found next URL by keyword '{keyword}': {next_url}")
                            # Log this discovered URL
                            self.log_discovered_url(next_url, 'pop' if chart_type == 'week' else 'rock', f'keyword_{keyword}')
                            return next_url
            
            # Strategy 3: Look for any URL with higher week number
            potential_next_urls = []
            for link in links:
                link_match = re.search(rf'{chart_type}(\d{{4}})\.html', link['href'])
                if link_match:
                    link_week_num = int(link_match.group(1))
                    if link_week_num > current_week_num:
                        potential_next_urls.append((link_week_num, link['href']))
            
            if potential_next_urls:
                # Sort by week number and take the smallest one greater than current
                potential_next_urls.sort(key=lambda x: x[0])
                next_url = potential_next_urls[0][1]
                if next_url.startswith('/'):
                    next_url = urljoin(self.base_url, next_url)
                print(f"Found next URL by week number comparison: {next_url}")
                # Log this discovered URL
                self.log_discovered_url(next_url, 'pop' if chart_type == 'week' else 'rock', 'week_comparison')
                return next_url
            
            # Strategy 4: Look in navigation elements specifically
            nav_links = await page.evaluate('''
                () => {
                    const navElements = document.querySelectorAll('nav, .navigation, .nav, [class*="nav"], [id*="nav"]');
                    const links = [];
                    navElements.forEach(nav => {
                        const navLinks = nav.querySelectorAll('a[href]');
                        navLinks.forEach(link => {
                            links.push({
                                href: link.href,
                                text: link.textContent.trim().toLowerCase(),
                                parent: nav.className || nav.tagName
                            });
                        });
                    });
                    return links;
                }
            ''')
            
            for link in nav_links:
                if any(keyword in link['text'] for keyword in ['next', 'forward', '→', '>']):
                    if (chart_type == 'week' and 'week' in link['href']) or (chart_type == 'rock' and 'rock' in link['href']):
                        next_url = link['href']
                        if next_url.startswith('/'):
                            next_url = urljoin(self.base_url, next_url)
                        print(f"Found next URL in navigation: {next_url}")
                        # Log this discovered URL
                        self.log_discovered_url(next_url, 'pop' if chart_type == 'week' else 'rock', 'navigation')
                        return next_url
            
            # Strategy 5: Look for pagination or arrow links
            arrow_links = await page.evaluate('''
                () => {
                    const arrowSelectors = [
                        'a[title*="next"]', 'a[title*="Next"]',
                        'a[alt*="next"]', 'a[alt*="Next"]',
                        'a:contains("►")', 'a:contains("→")', 'a:contains(">")',
                        '.next a', '.pagination a', '[class*="next"] a'
                    ];
                    const links = [];
                    document.querySelectorAll('a[href]').forEach(link => {
                        const text = link.textContent.trim();
                        const title = link.title || '';
                        const alt = link.alt || '';
                        if (text.includes('►') || text.includes('→') || text.includes('>') ||
                            title.toLowerCase().includes('next') || alt.toLowerCase().includes('next')) {
                            links.push({
                                href: link.href,
                                text: text,
                                title: title,
                                alt: alt
                            });
                        }
                    });
                    return links;
                }
            ''')
            
            for link in arrow_links:
                if (chart_type == 'week' and 'week' in link['href']) or (chart_type == 'rock' and 'rock' in link['href']):
                    next_url = link['href']
                    if next_url.startswith('/'):
                        next_url = urljoin(self.base_url, next_url)
                    print(f"Found next URL by arrow/pagination: {next_url}")
                    # Log this discovered URL
                    self.log_discovered_url(next_url, 'pop' if chart_type == 'week' else 'rock', 'arrow_pagination')
                    return next_url
            
            print(f"No next URL found using any strategy")
            print(f"Available links with {chart_type}:")
            for link in links:
                if chart_type in link['href']:
                    print(f"  - {link['href']} (text: '{link['text']}')")
            
            return None
            
        except Exception as e:
            print(f"Error finding next week URL: {e}")
            return None
    
    async def get_next_url_with_fallback(self, current_url, chart_type):
        """Get next URL with multiple fallback strategies to ensure no URLs are missed"""
        
        # Strategy 1: Try to find next URL from the current page
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()
            
            try:
                await page.goto(current_url, timeout=30000)
                await page.wait_for_timeout(2000)
                next_url = await self.find_next_week_url(page, current_url)
                
                if next_url:
                    await browser.close()
                    return next_url
                    
            except Exception as e:
                print(f"Error loading page for next URL search: {e}")
            finally:
                await browser.close()
        
        # Strategy 2: Generate next URL mathematically if page method fails
        current_match = re.search(r'(?:week|rock)(\d{4})\.html', current_url)
        if current_match:
            current_week_num = int(current_match.group(1))
            next_week_num = current_week_num + 1
            next_week_str = f"{next_week_num:04d}"
            
            # Extract decade from current URL
            decade_match = re.search(r'/charts/(\d{2})/', current_url)
            if decade_match:
                decade = decade_match.group(1)
                chart_prefix = 'rock' if chart_type == 'rock' else 'week'
                generated_url = f"https://tunecaster.com/charts/{decade}/{chart_prefix}{next_week_str}.html"
                
                print(f"Generated fallback URL: {generated_url}")
                
                # Verify if this URL exists by trying to load it
                try:
                    async with async_playwright() as p:
                        browser = await p.chromium.launch(headless=True)
                        context = await browser.new_context()
                        page = await context.new_page()
                        
                        response = await page.goto(generated_url, timeout=20000)
                        if response and response.status == 200:
                            print(f"Verified generated URL exists: {generated_url}")
                            # Log this discovered URL
                            self.log_discovered_url(generated_url, chart_type, 'generated_fallback')
                            await browser.close()
                            return generated_url
                        else:
                            print(f"Generated URL returned status {response.status if response else 'None'}")
                        
                        await browser.close()
                except Exception as e:
                    print(f"Could not verify generated URL: {e}")
        
        # Strategy 3: Try different decade formats if the simple increment doesn't work
        if current_match:
            current_week_num = int(current_match.group(1))
            
            # Handle decade transitions (e.g., from 99xx to 00xx)
            year_part = current_week_num // 100
            week_part = current_week_num % 100
            
            # Try next week within same year
            if week_part < 52:  # Assuming max 52 weeks per year
                next_week_num = current_week_num + 1
            else:
                # Move to next year, week 1
                next_year = year_part + 1
                if next_year >= 100:  # Handle century transition
                    next_year = 0
                next_week_num = next_year * 100 + 1
            
            next_week_str = f"{next_week_num:04d}"
            
            # Try different decades
            decades_to_try = ['10', '20', '30', '40', '50', '60', '70', '80', '90']
            
            for decade in decades_to_try:
                chart_prefix = 'rock' if chart_type == 'rock' else 'week'
                test_url = f"https://tunecaster.com/charts/{decade}/{chart_prefix}{next_week_str}.html"
                
                try:
                    async with async_playwright() as p:
                        browser = await p.chromium.launch(headless=True)
                        context = await browser.new_context()
                        page = await context.new_page()
                        
                        response = await page.goto(test_url, timeout=15000)
                        if response and response.status == 200:
                            print(f"Found next URL in decade {decade}: {test_url}")
                            # Log this discovered URL
                            self.log_discovered_url(test_url, chart_type, f'decade_search_{decade}')
                            await browser.close()
                            return test_url
                        
                        await browser.close()
                except Exception as e:
                    continue  # Try next decade
        
        print(f"Could not find next URL for {current_url} using any strategy")
        return None
    
    async def scrape_chart_and_get_next(self, url, chart_type):
        """Scrape a single chart and return both the data and next URL"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            page = await context.new_page()
            
            try:
                await page.goto(url, wait_until='networkidle', timeout=30000)
                await page.wait_for_timeout(3000)
                
                # Scrape chart data
                html_content = await page.content()
                chart_data = self.parse_chart(html_content, url, chart_type)
                
                if chart_data and len(chart_data['records']) < 10:
                    chart_data = await self.parse_chart_alternative(page, url, chart_type)
                
                # Find next week URL
                next_url = await self.find_next_week_url(page, url)
                
                return chart_data, next_url
                
            except Exception as e:
                print(f"Error scraping {url}: {e}")
                return None, None
            finally:
                await browser.close()
    
    async def scrape_sequential_charts(self):
        """Scrape charts sequentially following next week links"""
        print("\nStarting sequential chart scraping...")
        print("Phase 1: Pop charts from begin to end URL")
        print("Phase 2: Rock charts from begin to end URL")
        
        self.load_progress()
        
        # PHASE 1: Pop Charts
        print(f"\nPHASE 1: SCRAPING POP CHARTS")
        print(f"Starting from: {self.current_pop_url}")
        print(f"Target end URL: {self.pop_end_url}")
        
        current_url = self.current_pop_url
        pop_count = 0
        
        while current_url:
            pop_count += 1
            
            # Check if already processed
            if current_url in self.processed_urls:
                print(f"Pop {pop_count}: SKIPPED - {current_url}")
                # If we've reached the end, break
                if current_url == self.pop_end_url:
                    print("Reached pop end URL (already processed)")
                    break
                # Get next URL to continue
                current_url = await self.get_next_url_with_fallback(current_url, 'pop')
                continue
            
            print(f"Pop {pop_count}: Scraping - {current_url}")
            
            try:
                chart_data, next_url = await self.scrape_chart_and_get_next(current_url, 'pop')
                
                if chart_data:
                    records_count = len(chart_data['records'])
                    self.all_chart_data.append(chart_data)
                    
                    chart_date = chart_data['chart_info']['chart_date']
                    print(f"Chart Date: {chart_date} | Records: {records_count}")
                    
                    # Show top 3 records
                    for record in chart_data['records'][:3]:
                        rank = record['rank']
                        title = record['title']
                        artist = record['artist'] if record['artist'] else '[No Artist]'
                        print(f"   {rank}. {title} - {artist}")
                    
                    self.save_progress(current_url, 'pop')
                    self.save_incremental_data()
                    print(f"Success: {records_count} records")
                    
                else:
                    print("Failed to scrape chart data")
                    self.save_progress(current_url, 'pop')
                
                # Check if we've reached the end URL
                if current_url == self.pop_end_url:
                    print("Reached pop end URL!")
                    break
                
                # Move to next URL - use fallback if primary method fails
                if next_url:
                    print(f"Next URL found from page: {next_url}")
                    current_url = next_url
                else:
                    print("No next URL found on page, trying fallback methods...")
                    fallback_url = await self.get_next_url_with_fallback(current_url, 'pop')
                    if fallback_url:
                        current_url = fallback_url
                        print(f"Fallback URL found: {current_url}")
                    else:
                        print("No next URL found using any method, ending pop phase")
                        current_url = None
            
            except Exception as e:
                print(f"Error processing {current_url}: {e}")
                self.save_progress(current_url, 'pop')
                # Try to continue with next URL even after error
                fallback_url = await self.get_next_url_with_fallback(current_url, 'pop')
                if fallback_url:
                    current_url = fallback_url
                    print(f"Continuing with fallback URL after error: {current_url}")
                else:
                    break
            
            await asyncio.sleep(2)
        
        print(f"Pop phase completed! Processed {pop_count} charts")
        
        # PHASE 2: Rock Charts
        print(f"\nPHASE 2: SCRAPING ROCK CHARTS")
        print(f"Starting from: {self.current_rock_url}")
        print(f"Target end URL: {self.rock_end_url}")
        
        current_url = self.current_rock_url
        rock_count = 0
        
        while current_url:
            rock_count += 1
            
            # Check if already processed
            if current_url in self.processed_urls:
                print(f"Rock {rock_count}: SKIPPED - {current_url}")
                # If we've reached the end, break
                if current_url == self.rock_end_url:
                    print("Reached rock end URL (already processed)")
                    break
                # Get next URL to continue
                current_url = await self.get_next_url_with_fallback(current_url, 'rock')
                continue
            
            print(f"Rock {rock_count}: Scraping - {current_url}")
            
            try:
                chart_data, next_url = await self.scrape_chart_and_get_next(current_url, 'rock')
                
                if chart_data:
                    records_count = len(chart_data['records'])
                    self.all_chart_data.append(chart_data)
                    
                    chart_date = chart_data['chart_info']['chart_date']
                    print(f"Chart Date: {chart_date} | Records: {records_count}")
                    
                    # Show top 3 records
                    for record in chart_data['records'][:3]:
                        rank = record['rank']
                        title = record['title']
                        artist = record['artist'] if record['artist'] else '[No Artist]'
                        print(f"   {rank}. {title} - {artist}")
                    
                    self.save_progress(current_url, 'rock')
                    self.save_incremental_data()
                    print(f"Success: {records_count} records")
                    
                else:
                    print("Failed to scrape chart data")
                    self.save_progress(current_url, 'rock')
                
                # Check if we've reached the end URL
                if current_url == self.rock_end_url:
                    print("Reached rock end URL!")
                    break
                
                # Move to next URL - use fallback if primary method fails
                if next_url:
                    print(f"Next URL found from page: {next_url}")
                    current_url = next_url
                else:
                    print("No next URL found on page, trying fallback methods...")
                    fallback_url = await self.get_next_url_with_fallback(current_url, 'rock')
                    if fallback_url:
                        current_url = fallback_url
                        print(f"Fallback URL found: {current_url}")
                    else:
                        print("No next URL found using any method, ending rock phase")
                        current_url = None
            
            except Exception as e:
                print(f"Error processing {current_url}: {e}")
                self.save_progress(current_url, 'rock')
                # Try to continue with next URL even after error
                fallback_url = await self.get_next_url_with_fallback(current_url, 'rock')
                if fallback_url:
                    current_url = fallback_url
                    print(f"Continuing with fallback URL after error: {current_url}")
                else:
                    break
            
            await asyncio.sleep(2)
        
        print(f"Rock phase completed! Processed {rock_count} charts")
    
    async def parse_chart_alternative(self, page, url, chart_type):
        html_content = await page.content()
        soup = BeautifulSoup(html_content, 'html.parser')
        chart_date = self.extract_chart_date_from_page(soup)
        
        if chart_date is None:
            chart_date = self.extract_chart_date_from_url(url)
            
        if chart_date is None:
            print(f"Skipping alternative parsing due to invalid date: {url}")
            return None
        
        try:
            chart_data = await page.evaluate('''
                () => {
                    const songs = [];
                    const positionCells = document.querySelectorAll('td.thisWeek');
                    
                    positionCells.forEach(posCell => {
                        const posText = posCell.textContent.trim();
                        if (posText && posText !== 'TW' && /^\\d+$/.test(posText)) {
                            const position = parseInt(posText);
                            let title = '';
                            let artist = '';
                            
                            const row = posCell.closest('tr');
                            if (row) {
                                const titleCell = row.querySelector('td.title20') || row.querySelector('td.titleBoth20');
                                if (titleCell) {
                                    const titleLink = titleCell.querySelector('a.songLink');
                                    title = titleLink ? titleLink.textContent.trim() : titleCell.textContent.trim();
                                }
                            }
                            
                            let currentTable = posCell.closest('table');
                            let nextTable = currentTable ? currentTable.nextElementSibling : null;
                            let attempts = 0;
                            let foundArtist = false;
                            
                            while (nextTable && attempts < 15 && !foundArtist) {
                                if (nextTable.tagName === 'TABLE') {
                                    const artistCell = nextTable.querySelector('td.artist20');
                                    if (artistCell) {
                                        artist = artistCell.textContent.trim();
                                        if (artist) {
                                            foundArtist = true;
                                            break;
                                        }
                                    }
                                    
                                    const allCells = nextTable.querySelectorAll('td');
                                    for (let cell of allCells) {
                                        const cellText = cell.textContent.trim();
                                        if (cellText &&
                                            !cellText.match(/^\\d+$/) &&
                                            !cellText.includes('http') &&
                                            cellText !== '|' &&
                                            cellText !== '![]()') {

                                            if (!artist) {
                                                artist = cellText;
                                                break;
                                            }
                                        }
                                    }
                                    
                                    if (artist) {
                                        foundArtist = true;
                                        break;
                                    }
                                }
                                nextTable = nextTable.nextElementSibling;
                                attempts++;
                            }
                            
                            if (title) {
                                songs.push({
                                    position: position,
                                    title: title,
                                    artist: artist || ''
                                });
                            }
                        }
                    });
                    
                    return songs;
                }
            ''')
            
            if chart_data:
                chart_data.sort(key=lambda x: x.get('position', 999))
                
                for song in chart_data:
                    if song.get('artist'):
                        song['artist'] = self.parse_multiple_artists(song['artist'])
                    else:
                        song['artist'] = ""
                
                return {
                    'chart_info': {
                        'chart_type': chart_type,
                        'chart_date': chart_date,
                        'url': url
                    },
                    'records': [
                        {
                            "id": self.generate_record_id(url, song['position']),
                            "chart_date": chart_date,
                            "rank": song['position'],
                            "title": song['title'],
                            "artist": song['artist'],
                            "url": url
                        }
                        for song in chart_data
                    ]
                }
            
        except Exception as e:
            print(f"Alternative parsing failed: {e}")
        
        return None
    
    def parse_chart(self, html_content, url, chart_type):
        soup = BeautifulSoup(html_content, 'html.parser')
        songs = self.extract_songs_from_html(soup)
        
        chart_date = self.extract_chart_date_from_page(soup)
        
        if chart_date is None:
            chart_date = self.extract_chart_date_from_url(url)
            
        if chart_date is None:
            print(f"Skipping chart due to invalid date: {url}")
            return None
            
        records = []
        
        for song in songs:
            record = {
                "id": self.generate_record_id(url, song['position']),
                "chart_date": chart_date,
                "rank": song['position'],
                "title": song['title'],
                "artist": song['artist'],
                "url": url
            }
            records.append(record)
        
        return {
            'chart_info': {
                'chart_type': chart_type,
                'chart_date': chart_date,
                'url': url
            },
            'records': records
        }
    
    def extract_songs_from_html(self, soup):
        songs = []
        songs.extend(self.extract_using_table_structure(soup))
        
        text_songs = self.extract_using_sequential_parsing(soup)
        for song in text_songs:
            if not any(s['position'] == song['position'] for s in songs):
                songs.append(song)
        
        unique_songs = self.clean_songs(songs)
        unique_songs.sort(key=lambda x: x.get('position', 999))
        
        return unique_songs
    
    def extract_using_table_structure(self, soup):
        songs = []
        tables = soup.find_all('table', class_='t2')
        
        i = 0
        while i < len(tables):
            table = tables[i]
            tw_cell = table.find('td', class_='thisWeek')
            title_cell = table.find('td', class_='title20') or table.find('td', class_='titleBoth20')
            
            if tw_cell and title_cell:
                tw_text = tw_cell.get_text().strip()
                
                if tw_text == 'TW' or not tw_text.isdigit():
                    i += 1
                    continue
                
                tw_position = int(tw_text)
                title = self.extract_title_from_cell(title_cell)
                artist = self.find_artist_in_next_tables(tables, i + 1)
                
                if title:
                    if not isinstance(artist, str):
                        artist = str(artist) if artist else ""

                    artists = self.parse_multiple_artists(artist)
                    songs.append({
                        'position': tw_position,
                        'title': title,
                        'artist': artists
                    })
            
            i += 1
        
        return songs
    
    def extract_using_sequential_parsing(self, soup):
        songs = []
        page_text = soup.get_text()
        lines = [line.strip() for line in page_text.split('\n') if line.strip()]
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            if any(skip in line.lower() for skip in ['download', 'amazon', 'img', 'src=', 'http', '![]']):
                i += 1
                continue
            
            tw_peaks_match = re.search(r'\[TW\]peaks.*?\[(?:rock|pop)\].*?(\d+)\s*\|\s*(\d+)\s*\|\s*([^|]+)', line)
            if tw_peaks_match:
                tw_position = int(tw_peaks_match.group(2))
                title = tw_peaks_match.group(3).strip()
                artist = self.find_artist_in_text_lines(lines, i + 1)
                
                if title:
                    if not isinstance(artist, str):
                        artist = str(artist) if artist else ""

                    artists = self.parse_multiple_artists(artist)
                    songs.append({
                        'position': tw_position,
                        'title': title,
                        'artist': artists
                    })
                i += 1
                continue
            
            standard_match = re.match(r'^(\d+)\s*\|\s*(\d+)\s*\|\s*([^|]*)', line)
            if standard_match:
                tw_position = int(standard_match.group(2))
                title = standard_match.group(3).strip()
                
                if not title or len(title.strip()) < 2:
                    title = self.find_title_in_next_lines(lines, i + 1)
                
                artist = self.find_artist_in_text_lines(lines, i + 1)
                
                if title and len(title.strip()) >= 2:
                    if not isinstance(artist, str):
                        artist = str(artist) if artist else ""

                    artists = self.parse_multiple_artists(artist)
                    songs.append({
                        'position': tw_position,
                        'title': title,
                        'artist': artists
                    })
                i += 1
                continue
            
            new_entry_match = re.match(r'^\-\s*\|\s*(\d+)\s*\|\s*([^|]+)', line)
            if new_entry_match:
                tw_position = int(new_entry_match.group(1))
                title = new_entry_match.group(2).strip()
                artist = self.find_artist_in_text_lines(lines, i + 1)
                
                if title:
                    if not isinstance(artist, str):
                        artist = str(artist) if artist else ""

                    artists = self.parse_multiple_artists(artist)
                    songs.append({
                        'position': tw_position,
                        'title': title,
                        'artist': artists
                    })
                i += 1
                continue
            
            i += 1
        
        return songs
    
    def find_title_in_next_lines(self, lines, start_index):
        for j in range(start_index, min(start_index + 3, len(lines))):
            if j >= len(lines):
                break
            
            line = lines[j].strip()
            
            if (not line or 
                any(skip in line.lower() for skip in ['download', 'amazon', 'img', 'src=', 'http', '![]']) or
                re.match(r'^[\|\s\-]*, line) or
                re.match(r'^\d+\s*\|\s*\d+', line) or
                line.isdigit()):
                continue
            
            clean_line = re.sub(r'^[\|\s]+|[\|\s]+, '', line)
            clean_line = re.sub(r'\s+', ' ', clean_line).strip()
            
            if clean_line and len(clean_line) >= 2:
                return clean_line
        
        return ""
    
    def find_artist_in_text_lines(self, lines, start_index):
        for j in range(start_index, min(start_index + 12, len(lines))):
            if j >= len(lines):
                break
            
            line = lines[j].strip()
            
            if (not line or
                re.match(r'^[\|\s\-]*, line) or
                re.match(r'^(\d+|\-)\s*\|\s*(\d+)', line) or
                line.isdigit() or
                'http' in line.lower()):
                continue

            clean_line = re.sub(r'^[\|\s]+|[\|\s]+, '', line)
            clean_line = re.sub(r'\s+', ' ', clean_line).strip()

            if clean_line and len(clean_line) > 0:
                return clean_line
        
        return ""
    
    def extract_chart_date_from_page(self, soup):
        try:
            month_names = {
                'january': 1, 'jan': 1,
                'february': 2, 'feb': 2,
                'march': 3, 'mar': 3,
                'april': 4, 'apr': 4,
                'may': 5,
                'june': 6, 'jun': 6,
                'july': 7, 'jul': 7,
                'august': 8, 'aug': 8,
                'september': 9, 'sep': 9, 'sept': 9,
                'october': 10, 'oct': 10,
                'november': 11, 'nov': 11,
                'december': 12, 'dec': 12
            }
            
            headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            
            for heading in headings:
                heading_text = heading.get_text().strip()
                
                date_patterns = [
                    r'for\s+([A-Za-z]+)\s+(\d{1,2}),?\s+(\d{4})',
                    r'([A-Za-z]+)\s+(\d{1,2}),?\s+(\d{4})',
                    r'(\d{1,2})\s+([A-Za-z]+)\s+(\d{4})',
                    r'([A-Za-z]{3,9})\s+(\d{1,2}),?\s+(\d{4})'
                ]
                
                for pattern in date_patterns:
                    date_match = re.search(pattern, heading_text, re.IGNORECASE)
                    if date_match:
                        groups = date_match.groups()
                        
                        try:
                            if len(groups) == 3 and groups[0].lower() in month_names:
                                month_str = groups[0].lower()
                                day = int(groups[1])
                                year = int(groups[2])
                                month = month_names[month_str]
                                
                                from datetime import datetime
                                parsed_date = datetime(year, month, day)
                                return parsed_date.strftime('%Y-%m-%d')
                            
                            elif len(groups) == 3 and groups[1].lower() in month_names:
                                day = int(groups[0])
                                month_str = groups[1].lower()
                                year = int(groups[2])
                                month = month_names[month_str]
                                
                                from datetime import datetime
                                parsed_date = datetime(year, month, day)
                                return parsed_date.strftime('%Y-%m-%d')
                                
                        except (ValueError, KeyError):
                            continue
            
        except Exception as e:
            print(f"Error extracting date from page: {e}")
        
        return None
    
    def extract_chart_date_from_url(self, url):
        match = re.search(r'/charts/(\d{2})/(?:rock|week)(\d{4})\.html', url)
        if match:
            decade = int(match.group(1))
            week_number = match.group(2)
            year_suffix = int(week_number[:2])
            week = int(week_number[2:])
            
            if decade >= 60 and decade <= 99:
                full_year = 1900 + year_suffix
            elif decade >= 0 and decade <= 20:
                full_year = 2000 + year_suffix
            else:
                full_year = 1900 + year_suffix if year_suffix >= 60 else 2000 + year_suffix
            
            try:
                import datetime
                jan_1 = datetime.date(full_year, 1, 1)
                days_to_monday = (7 - jan_1.weekday()) % 7
                first_monday = jan_1 + datetime.timedelta(days=days_to_monday)
                week_start = first_monday + datetime.timedelta(weeks=week-1)
                return week_start.strftime('%Y-%m-%d')
            except (ValueError, OverflowError):
                return f"{full_year}-01-01"
        
        print(f"Error: Could not extract date from URL: {url}")
        return None
    
    def generate_record_id(self, url, position):
        url_match = re.search(r'(?:rock|week)(\d{4})\.html', url)
        chart_id = url_match.group(1) if url_match else "0000"
        chart_type = "rock" if "rock" in url else "pop"
        return f"{chart_type}_{chart_id}_{position:03d}"
        
    def extract_title_from_cell(self, title_cell):
        link = title_cell.find('a', class_='songLink')
        if link:
            return link.get_text().strip()
        
        title = title_cell.get_text().strip()
        return re.sub(r'\s+', ' ', title).strip() if title else ""
    
    def find_artist_in_next_tables(self, tables, start_index):
        for j in range(start_index, min(start_index + 20, len(tables))):
            if j >= len(tables):
                break
            
            table = tables[j]
            artist_cell = table.find('td', class_='artist20')
            
            if artist_cell:
                artist = self.extract_artist_from_cell(artist_cell)
                if artist:
                    return artist
            
            all_cells = table.find_all('td')
            for cell in all_cells:
                cell_text = cell.get_text().strip()

                if (not cell_text or
                    cell_text.isdigit() or
                    cell_text in ['-', '|'] or
                    'http' in cell_text.lower() or
                    cell_text == '![]()'):
                    continue

                if cell_text and len(cell_text) > 0:
                    return cell_text
        
        return ""
    
    def extract_artist_from_cell(self, artist_cell):
        """Extract artist name exactly as it appears in the cell"""
        artist_text = artist_cell.get_text().strip()
        return re.sub(r'\s+', ' ', artist_text).strip() if artist_text else ""
    
    def parse_multiple_artists(self, artist_text):
        """Return artist name exactly as it appears on the website without any filtering"""
        if not artist_text:
            return ""

        if not isinstance(artist_text, str):
            artist_text = str(artist_text)

        artist_text = artist_text.strip()
        return artist_text
    
    def clean_songs(self, songs):
        seen_positions = set()
        unique_songs = []

        for song in songs:
            if not song or not song.get('position'):
                continue

            position = song['position']
            if position in seen_positions:
                continue

            title = song.get('title', '').strip()
            artist_data = song.get('artist', '')

            if not title:
                continue

            if isinstance(artist_data, str):
                artists = self.parse_multiple_artists(artist_data)
            else:
                artists = str(artist_data) if artist_data else ""

            unique_songs.append({
                'position': position,
                'title': title,
                'artist': artists
            })

            seen_positions.add(position)

        return unique_songs
    
    def save_incremental_data(self):
        try:
            os.makedirs('data', exist_ok=True)
            filename = 'data/charts_data.csv'
            file_exists = os.path.exists(filename)
            
            import csv
            mode = 'a' if file_exists else 'w'
            with open(filename, mode, encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                if not file_exists:
                    writer.writerow(['chart_date', 'chart_type', 'rank', 'title', 'artist', 'url'])
                
                for chart in self.all_chart_data[-1:]:
                    chart_type = chart['chart_info']['chart_type']
                    for record in chart['records']:
                        writer.writerow([
                            record['chart_date'],
                            chart_type,
                            record['rank'],
                            record['title'],
                            record['artist'],
                            record['url']
                        ])
            
            total_charts = len(self.all_chart_data)
            total_records = sum(len(chart.get('records', [])) for chart in self.all_chart_data)
            pop_charts = len([c for c in self.all_chart_data if c['chart_info']['chart_type'] == 'pop'])
            rock_charts = len([c for c in self.all_chart_data if c['chart_info']['chart_type'] == 'rock'])
            
            print(f"Saved: {total_charts} charts, {total_records} records (Rock: {rock_charts}, Pop: {pop_charts})")
            
        except Exception as e:
            print(f"Save failed: {e}")
    
    def print_final_summary(self):
        if not self.all_chart_data:
            print("No data to summarize")
            return
        
        pop_count = len([c for c in self.all_chart_data if c['chart_info']['chart_type'] == 'pop'])
        rock_count = len([c for c in self.all_chart_data if c['chart_info']['chart_type'] == 'rock'])
        total_records = sum(len(chart.get('records', [])) for chart in self.all_chart_data)

        print("\nTUNECASTER SEQUENTIAL SCRAPING COMPLETE")
        print("="*60)
        print(f"Pop Charts Scraped: {pop_count}")
        print(f"Rock Charts Scraped: {rock_count}")
        print(f"Total Charts Scraped: {len(self.all_chart_data)}")
        print(f"Total Records: {total_records}")
        print()
        print("URL DISCOVERY STATISTICS:")
        print(f"Total Pop URLs Discovered: {len(self.all_discovered_pop_urls)}")
        print(f"Total Rock URLs Discovered: {len(self.all_discovered_rock_urls)}")
        print(f"Total URLs Processed: {len(self.processed_urls)}")
        print()
        print("FILES CREATED:")
        print(f"Data File: data/charts_data.csv")
        print(f"Progress File: data/scraper_progress.json")
        print(f"URL Log File: data/url_log.txt")
        print("="*60)
        print("PROCESSING METHOD: SEQUENTIAL (NEXT WEEK LINKS)")
        print("ORDER: POP CHARTS FIRST, THEN ROCK CHARTS")
        
        # Show some example URLs that were discovered
        if self.all_discovered_pop_urls:
            print(f"\nSAMPLE POP URLS DISCOVERED ({min(5, len(self.all_discovered_pop_urls))} of {len(self.all_discovered_pop_urls)}):")
            for i, url in enumerate(self.all_discovered_pop_urls[:5], 1):
                status = "✓ PROCESSED" if url in self.processed_urls else "⏳ PENDING"
                print(f"  {i}. {url} - {status}")
        
        if self.all_discovered_rock_urls:
            print(f"\nSAMPLE ROCK URLS DISCOVERED ({min(5, len(self.all_discovered_rock_urls))} of {len(self.all_discovered_rock_urls)}):")
            for i, url in enumerate(self.all_discovered_rock_urls[:5], 1):
                status = "✓ PROCESSED" if url in self.processed_urls else "⏳ PENDING"
                print(f"  {i}. {url} - {status}")
        
        # Check for any URLs that were discovered but not processed
        unprocessed_pop = [url for url in self.all_discovered_pop_urls if url not in self.processed_urls]
        unprocessed_rock = [url for url in self.all_discovered_rock_urls if url not in self.processed_urls]
        
        if unprocessed_pop or unprocessed_rock:
            print(f"\n⚠️  UNPROCESSED URLS FOUND:")
            print(f"Unprocessed Pop URLs: {len(unprocessed_pop)}")
            print(f"Unprocessed Rock URLs: {len(unprocessed_rock)}")
            print("These URLs were discovered but not processed. Run the script again to continue.")
        else:
            print(f"\n✅ ALL DISCOVERED URLS WERE PROCESSED!")
        
        print("="*60)

async def main():
    scraper = TuneCasterSequentialScraper()
    
    print("TuneCaster Sequential Scraper")
    print("Following next week links from begin to end URLs")
    print("="*60)
    print(f"Pop: {scraper.pop_begin_url} → {scraper.pop_end_url}")
    print(f"Rock: {scraper.rock_begin_url} → {scraper.rock_end_url}")
    print("="*60)
    
    try:
        await scraper.scrape_sequential_charts()
        scraper.print_final_summary()
        
        print("Scraping completed successfully!")
        
    except KeyboardInterrupt:
        print("\nScraping interrupted")
        if scraper.all_chart_data:
            scraper.print_final_summary()
        print("Run script again to resume from where it left off")
    
    except Exception as e:
        print(f"Error: {e}")
        if scraper.all_chart_data:
            scraper.print_final_summary()

if __name__ == "__main__":
    asyncio.run(main())